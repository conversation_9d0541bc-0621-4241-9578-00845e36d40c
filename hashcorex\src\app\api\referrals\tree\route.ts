import { NextRequest, NextResponse } from 'next/server';
import { authenticateRequest } from '@/lib/auth';
import { getBinaryTreeStructure } from '@/lib/referral';
import { referralDb, binaryPointsDb } from '@/lib/database';

// GET - Fetch user's binary tree structure
export async function GET(request: NextRequest) {
  try {
    const { authenticated, user } = await authenticateRequest(request);

    if (!authenticated || !user) {
      return NextResponse.json(
        { success: false, error: 'Not authenticated' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const depth = parseInt(searchParams.get('depth') || '3');

    // Get binary tree structure
    const treeStructure = await getBinaryTreeStructure(user.id, Math.min(depth, 5)); // Max depth 5

    // Get user's referral statistics
    const directReferrals = await referralDb.findByReferrerId(user.id);
    const binaryPoints = await binaryPointsDb.findByUserId(user.id);

    // Calculate referral statistics
    const leftReferrals = directReferrals.filter(r => r.placementSide === 'LEFT').length;
    const rightReferrals = directReferrals.filter(r => r.placementSide === 'RIGHT').length;
    const totalCommissions = directReferrals.reduce((sum, r) => sum + r.commissionEarned, 0);

    return NextResponse.json({
      success: true,
      data: {
        treeStructure,
        statistics: {
          totalDirectReferrals: directReferrals.length,
          leftReferrals,
          rightReferrals,
          totalCommissions,
          binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },
        },
        referralLinks: {
          left: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=left`,
          right: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}&side=right`,
          general: `${process.env.NEXT_PUBLIC_APP_URL}/register?ref=${user.referralId}`,
        },
      },
    });

  } catch (error: any) {
    console.error('Binary tree fetch error:', error);
    
    return NextResponse.json(
      { success: false, error: 'Failed to fetch binary tree' },
      { status: 500 }
    );
  }
}
