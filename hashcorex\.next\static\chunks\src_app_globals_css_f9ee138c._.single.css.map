{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap');\n@layer properties;\n.pointer-events-none {\n  pointer-events: none;\n}\n.visible {\n  visibility: visible;\n}\n.absolute {\n  position: absolute;\n}\n.fixed {\n  position: fixed;\n}\n.relative {\n  position: relative;\n}\n.static {\n  position: static;\n}\n.left-1\\/4 {\n  left: calc(1/4 * 100%);\n}\n.z-10 {\n  z-index: 10;\n}\n.z-40 {\n  z-index: 40;\n}\n.z-50 {\n  z-index: 50;\n}\n.mx-auto {\n  margin-inline: auto;\n}\n.block {\n  display: block;\n}\n.flex {\n  display: flex;\n}\n.grid {\n  display: grid;\n}\n.hidden {\n  display: none;\n}\n.inline {\n  display: inline;\n}\n.inline-flex {\n  display: inline-flex;\n}\n.table {\n  display: table;\n}\n.h-full {\n  height: 100%;\n}\n.min-h-screen {\n  min-height: 100vh;\n}\n.w-full {\n  width: 100%;\n}\n.max-w-full {\n  max-width: 100%;\n}\n.flex-1 {\n  flex: 1;\n}\n.flex-shrink-0 {\n  flex-shrink: 0;\n}\n.grow {\n  flex-grow: 1;\n}\n.-translate-x-full {\n  --tw-translate-x: -100%;\n  translate: var(--tw-translate-x) var(--tw-translate-y);\n}\n.transform {\n  transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n}\n.cursor-pointer {\n  cursor: pointer;\n}\n.list-inside {\n  list-style-position: inside;\n}\n.list-disc {\n  list-style-type: disc;\n}\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n.grid-cols-2 {\n  grid-template-columns: repeat(2, minmax(0, 1fr));\n}\n.grid-cols-3 {\n  grid-template-columns: repeat(3, minmax(0, 1fr));\n}\n.grid-cols-4 {\n  grid-template-columns: repeat(4, minmax(0, 1fr));\n}\n.flex-col {\n  flex-direction: column;\n}\n.flex-col-reverse {\n  flex-direction: column-reverse;\n}\n.flex-row {\n  flex-direction: row;\n}\n.flex-row-reverse {\n  flex-direction: row-reverse;\n}\n.flex-nowrap {\n  flex-wrap: nowrap;\n}\n.flex-wrap {\n  flex-wrap: wrap;\n}\n.flex-wrap-reverse {\n  flex-wrap: wrap-reverse;\n}\n.items-baseline {\n  align-items: baseline;\n}\n.items-center {\n  align-items: center;\n}\n.items-end {\n  align-items: flex-end;\n}\n.items-start {\n  align-items: flex-start;\n}\n.items-stretch {\n  align-items: stretch;\n}\n.justify-around {\n  justify-content: space-around;\n}\n.justify-between {\n  justify-content: space-between;\n}\n.justify-center {\n  justify-content: center;\n}\n.justify-end {\n  justify-content: flex-end;\n}\n.justify-evenly {\n  justify-content: space-evenly;\n}\n.justify-start {\n  justify-content: flex-start;\n}\n.truncate {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.overflow-hidden {\n  overflow: hidden;\n}\n.overflow-x-auto {\n  overflow-x: auto;\n}\n.rounded-full {\n  border-radius: calc(infinity * 1px);\n}\n.rounded-l-none {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n.border {\n  border-style: var(--tw-border-style);\n  border-width: 1px;\n}\n.border-2 {\n  border-style: var(--tw-border-style);\n  border-width: 2px;\n}\n.border-t {\n  border-top-style: var(--tw-border-style);\n  border-top-width: 1px;\n}\n.border-b {\n  border-bottom-style: var(--tw-border-style);\n  border-bottom-width: 1px;\n}\n.border-dashed {\n  --tw-border-style: dashed;\n  border-style: dashed;\n}\n.bg-gradient-to-br {\n  --tw-gradient-position: to bottom right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.bg-gradient-to-r {\n  --tw-gradient-position: to right in oklab;\n  background-image: linear-gradient(var(--tw-gradient-stops));\n}\n.object-cover {\n  object-fit: cover;\n}\n.text-center {\n  text-align: center;\n}\n.text-left {\n  text-align: left;\n}\n.text-right {\n  text-align: right;\n}\n.leading-none {\n  --tw-leading: 1;\n  line-height: 1;\n}\n.capitalize {\n  text-transform: capitalize;\n}\n.lowercase {\n  text-transform: lowercase;\n}\n.uppercase {\n  text-transform: uppercase;\n}\n.underline-offset-4 {\n  text-underline-offset: 4px;\n}\n.antialiased {\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\n.opacity-5 {\n  opacity: 5%;\n}\n.opacity-90 {\n  opacity: 90%;\n}\n.outline {\n  outline-style: var(--tw-outline-style);\n  outline-width: 1px;\n}\n.transition-all {\n  transition-property: all;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-colors {\n  transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-opacity {\n  transition-property: opacity;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-shadow {\n  transition-property: box-shadow;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.transition-transform {\n  transition-property: transform, translate, scale, rotate;\n  transition-timing-function: var(--tw-ease, ease);\n  transition-duration: var(--tw-duration, 0s);\n}\n.delay-1000 {\n  transition-delay: 1000ms;\n}\n.delay-2000 {\n  transition-delay: 2000ms;\n}\n.duration-200 {\n  --tw-duration: 200ms;\n  transition-duration: 200ms;\n}\n.duration-300 {\n  --tw-duration: 300ms;\n  transition-duration: 300ms;\n}\n.hover\\:underline {\n  &:hover {\n    @media (hover: hover) {\n      text-decoration-line: underline;\n    }\n  }\n}\n.focus\\:border-transparent {\n  &:focus {\n    border-color: transparent;\n  }\n}\n.focus\\:ring-2 {\n  &:focus {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n}\n.focus\\:ring-offset-2 {\n  &:focus {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n}\n.focus\\:outline-none {\n  &:focus {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n}\n.disabled\\:pointer-events-none {\n  &:disabled {\n    pointer-events: none;\n  }\n}\n.disabled\\:cursor-not-allowed {\n  &:disabled {\n    cursor: not-allowed;\n  }\n}\n.disabled\\:opacity-50 {\n  &:disabled {\n    opacity: 50%;\n  }\n}\n:root {\n  --color-solar: #ffd60a;\n  --color-eco: #10b981;\n  --color-dark: #0f172a;\n  --color-gray: #6b7280;\n  --color-white: #ffffff;\n  --background: #ffffff;\n  --foreground: #0f172a;\n  --font-sans: 'Inter', 'Roboto', system-ui, sans-serif;\n  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;\n  --space-xs: 0.5rem;\n  --space-sm: 0.75rem;\n  --space-base: 1rem;\n  --space-lg: 1.25rem;\n  --space-xl: 1.5rem;\n  --space-2xl: 2rem;\n  --space-3xl: 3rem;\n  --radius-sm: 0.375rem;\n  --radius-base: 0.5rem;\n  --radius-lg: 0.75rem;\n  --radius-xl: 1rem;\n  --shadow-solar: 0 4px 14px 0 rgba(255, 214, 10, 0.39);\n  --shadow-eco: 0 4px 14px 0 rgba(16, 185, 129, 0.39);\n  --shadow-dark: 0 4px 14px 0 rgba(15, 23, 42, 0.39);\n}\n* {\n  box-sizing: border-box;\n}\nhtml {\n  scroll-behavior: smooth;\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: var(--font-sans);\n  font-size: 1rem;\n  line-height: 1.5;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}\nh1, h2, h3, h4, h5, h6 {\n  font-weight: 600;\n  line-height: 1.2;\n  color: var(--color-dark);\n}\nh1 {\n  font-size: 3rem;\n  font-weight: 800;\n}\nh2 {\n  font-size: 2.25rem;\n  font-weight: 700;\n}\nh3 {\n  font-size: 1.875rem;\n  font-weight: 600;\n}\nh4 {\n  font-size: 1.5rem;\n  font-weight: 600;\n}\nh5 {\n  font-size: 1.25rem;\n  font-weight: 600;\n}\nh6 {\n  font-size: 1.125rem;\n  font-weight: 600;\n}\np {\n  font-size: 1.125rem;\n  line-height: 1.6;\n  color: var(--color-gray);\n}\na {\n  color: var(--color-solar);\n  text-decoration: none;\n  transition: color 0.2s ease;\n}\na:hover {\n  color: #d97706;\n  text-decoration: underline;\n}\n*:focus {\n  outline: 2px solid var(--color-solar);\n  outline-offset: 2px;\n}\n::-webkit-scrollbar {\n  width: 8px;\n}\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n::-webkit-scrollbar-thumb {\n  background: var(--color-gray);\n  border-radius: 4px;\n}\n::-webkit-scrollbar-thumb:hover {\n  background: var(--color-dark);\n}\n.text-solar {\n  color: var(--color-solar);\n}\n.text-eco {\n  color: var(--color-eco);\n}\n.text-dark {\n  color: var(--color-dark);\n}\n.bg-solar {\n  background-color: var(--color-solar);\n}\n.bg-eco {\n  background-color: var(--color-eco);\n}\n.bg-dark {\n  background-color: var(--color-dark);\n}\n.animate-glow {\n  animation: glow 2s ease-in-out infinite alternate;\n}\n@keyframes glow {\n  from {\n    box-shadow: 0 0 5px var(--color-solar), 0 0 10px var(--color-solar), 0 0 15px var(--color-solar);\n  }\n  to {\n    box-shadow: 0 0 10px var(--color-solar), 0 0 20px var(--color-solar), 0 0 30px var(--color-solar);\n  }\n}\n.spinner {\n  border: 2px solid #f3f3f3;\n  border-top: 2px solid var(--color-solar);\n  border-radius: 50%;\n  width: 20px;\n  height: 20px;\n  animation: spin 1s linear infinite;\n}\n@keyframes spin {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-border-style: solid;\n      --tw-leading: initial;\n      --tw-outline-style: solid;\n      --tw-duration: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n    }\n  }\n}\n"], "names": [], "mappings": "AAEA;EAklBE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAllBJ;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;;AAMI;EAAuB;;;;;AAMzB;;;;AAKA;;;;;AAMA;;;;;AAMA;;;;;AAMA;;;;AAKA;;;;AAKA;;;;AAIF;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;;;;;AAQA;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA"}}]}