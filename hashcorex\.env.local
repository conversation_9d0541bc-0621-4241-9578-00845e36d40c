# Database Configuration
DATABASE_URL="postgresql://postgres:postgres@localhost:5432/hashcorex?schema=public"
DIRECT_URL="postgresql://postgres:postgres@localhost:5432/hashcorex?schema=public"

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY="AIzaSyA0Z_g18bOiEaDW2i8bO8mkPhM8c19V8A0"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="hashx-f35ed.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="hashx-f35ed"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="hashx-f35ed.firebasestorage.app"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="986475728569"
NEXT_PUBLIC_FIREBASE_APP_ID="1:986475728569:web:3d556efbf4892871ae9568"

# JWT Configuration
JWT_SECRET="super-secret-jwt-key-for-development-testing-only-change-in-production"
JWT_EXPIRES_IN="7d"

# Encryption Keys
ENCRYPTION_KEY="development-encryption-key-32chars"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="change-this-secure-password"

# Platform Settings
NEXT_PUBLIC_APP_NAME="HashCoreX"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Mining Configuration
DEFAULT_THS_PRICE="50"
DEFAULT_ROI_MIN="0.6"
DEFAULT_ROI_MAX="1.1"
MINIMUM_PURCHASE="50"
MINIMUM_WITHDRAWAL="10"

# Binary System Configuration
DIRECT_REFERRAL_BONUS="10"
BINARY_POOL_PERCENTAGE="30"
MAX_BINARY_POINTS_PER_SIDE="2000"

# USDT Configuration (for withdrawals)
USDT_NETWORK="TRC20"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Development
NODE_ENV="development"
