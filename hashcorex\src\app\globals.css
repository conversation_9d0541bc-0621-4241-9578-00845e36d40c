@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Roboto:wght@300;400;500;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* HashCoreX Design System Variables */
:root {
  /* Colors */
  --color-solar: #ffd60a;
  --color-eco: #10b981;
  --color-dark: #0f172a;
  --color-gray: #6b7280;
  --color-white: #ffffff;

  /* Background */
  --background: #ffffff;
  --foreground: #0f172a;

  /* Typography */
  --font-sans: 'Inter', 'Roboto', system-ui, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

  /* Spacing scale (1.2 modular scale) */
  --space-xs: 0.5rem;    /* 8px */
  --space-sm: 0.75rem;   /* 12px */
  --space-base: 1rem;    /* 16px */
  --space-lg: 1.25rem;   /* 20px */
  --space-xl: 1.5rem;    /* 24px */
  --space-2xl: 2rem;     /* 32px */
  --space-3xl: 3rem;     /* 48px */

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-base: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;

  /* Shadows */
  --shadow-solar: 0 4px 14px 0 rgba(255, 214, 10, 0.39);
  --shadow-eco: 0 4px 14px 0 rgba(16, 185, 129, 0.39);
  --shadow-dark: 0 4px 14px 0 rgba(15, 23, 42, 0.39);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  font-size: 1rem; /* 16px base */
  line-height: 1.5;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography Scale */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.2;
  color: var(--color-dark);
}

h1 {
  font-size: 3rem;     /* 48px */
  font-weight: 800;
}

h2 {
  font-size: 2.25rem;  /* 36px */
  font-weight: 700;
}

h3 {
  font-size: 1.875rem; /* 30px */
  font-weight: 600;
}

h4 {
  font-size: 1.5rem;   /* 24px - headings */
  font-weight: 600;
}

h5 {
  font-size: 1.25rem;  /* 20px */
  font-weight: 600;
}

h6 {
  font-size: 1.125rem; /* 18px - body */
  font-weight: 600;
}

p {
  font-size: 1.125rem; /* 18px body */
  line-height: 1.6;
  color: var(--color-gray);
}

/* Links */
a {
  color: var(--color-solar);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: #d97706;
  text-decoration: underline;
}

/* Focus styles */
*:focus {
  outline: 2px solid var(--color-solar);
  outline-offset: 2px;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: var(--color-gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-dark);
}

/* Utility classes */
.text-solar {
  color: var(--color-solar);
}

.text-eco {
  color: var(--color-eco);
}

.text-dark {
  color: var(--color-dark);
}

.bg-solar {
  background-color: var(--color-solar);
}

.bg-eco {
  background-color: var(--color-eco);
}

.bg-dark {
  background-color: var(--color-dark);
}

/* Animation utilities */
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 5px var(--color-solar), 0 0 10px var(--color-solar), 0 0 15px var(--color-solar);
  }
  to {
    box-shadow: 0 0 10px var(--color-solar), 0 0 20px var(--color-solar), 0 0 30px var(--color-solar);
  }
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid var(--color-solar);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
