'use client';

import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from '@/components/ui';
import { Container, Grid, GridItem, Flex } from '@/components/layout';
import { SolarPanel, MiningRig, Cryptocurrency, Leaf } from '@/components/icons';
import { ArrowRight, Shield, DollarSign, Users, Zap } from 'lucide-react';

export const HomePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="bg-white shadow-sm border-b border-gray-100">
        <Container>
          <Flex justify="between" align="center" className="h-16">
            <div className="flex items-center space-x-2">
              <SolarPanel className="h-8 w-8 text-solar-500" />
              <span className="text-2xl font-bold text-dark-900">HashCoreX</span>
            </div>
            <Flex align="center" gap={6}>
              <Link href="/about" className="text-gray-600 hover:text-dark-900 transition-colors">
                About
              </Link>
              <Link href="/how-it-works" className="text-gray-600 hover:text-dark-900 transition-colors">
                How It Works
              </Link>
              <Link href="/login">
                <Button variant="outline" size="sm">
                  Login
                </Button>
              </Link>
              <Link href="/register">
                <Button size="sm">
                  Get Started
                </Button>
              </Link>
            </Flex>
          </Flex>
        </Container>
      </nav>

      {/* Hero Section */}
      <section className="relative py-20 lg:py-32 overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-solar-50 to-eco-50">
          <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
          <div className="absolute top-20 left-10 animate-pulse">
            <SolarPanel className="h-16 w-16 text-solar-300" />
          </div>
          <div className="absolute top-40 right-20 animate-pulse delay-1000">
            <MiningRig className="h-12 w-12 text-eco-300" />
          </div>
          <div className="absolute bottom-20 left-1/4 animate-pulse delay-2000">
            <Cryptocurrency className="h-14 w-14 text-solar-400" />
          </div>
        </div>

        <Container className="relative z-10">
          <div className="text-center max-w-4xl mx-auto">
            <h1 className="text-5xl lg:text-7xl font-bold text-dark-900 mb-6">
              Invest in{' '}
              <span className="text-solar-500">Solar-Powered</span>{' '}
              Cloud Mining
            </h1>
            <p className="text-xl lg:text-2xl text-gray-600 mb-8 leading-relaxed">
              Join the future of sustainable cryptocurrency mining with our 
              eco-friendly, solar-powered data centers. Earn daily returns 
              while supporting renewable energy.
            </p>
            <Flex justify="center" gap={4} className="flex-col sm:flex-row">
              <Link href="/register">
                <Button size="xl" className="w-full sm:w-auto">
                  Start Mining Today
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Button>
              </Link>
              <Link href="/how-it-works">
                <Button variant="outline" size="xl" className="w-full sm:w-auto">
                  Learn More
                </Button>
              </Link>
            </Flex>
          </div>
        </Container>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <Container>
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark-900 mb-4">
              Why Choose HashCoreX?
            </h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Experience the perfect blend of profitability and sustainability 
              with our cutting-edge mining platform.
            </p>
          </div>

          <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={8}>
            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <Shield className="h-8 w-8 text-solar-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">KYC Verified</h3>
              <p className="text-gray-600">
                Secure and compliant platform with full KYC verification for all users.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4">
                <DollarSign className="h-8 w-8 text-eco-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">USDT Payments</h3>
              <p className="text-gray-600">
                Easy deposits and withdrawals using USDT (TRC20) for global accessibility.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4">
                <Zap className="h-8 w-8 text-solar-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Real ROI</h3>
              <p className="text-gray-600">
                Transparent daily returns with real mining operations and live statistics.
              </p>
            </div>

            <div className="text-center p-6 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4">
                <Leaf className="h-8 w-8 text-eco-600" />
              </div>
              <h3 className="text-xl font-semibold text-dark-900 mb-2">Eco Mining</h3>
              <p className="text-gray-600">
                100% solar-powered mining operations supporting sustainable cryptocurrency.
              </p>
            </div>
          </Grid>
        </Container>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-dark-900 text-white">
        <Container>
          <Grid cols={{ default: 1, md: 3 }} gap={8}>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-solar-400 mb-2">
                1,250+
              </div>
              <div className="text-xl text-gray-300">TH/s Sold</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-eco-400 mb-2">
                5,000+
              </div>
              <div className="text-xl text-gray-300">Active Users</div>
            </div>
            <div className="text-center">
              <div className="text-4xl lg:text-5xl font-bold text-solar-400 mb-2">
                98.5%
              </div>
              <div className="text-xl text-gray-300">Uptime</div>
            </div>
          </Grid>
        </Container>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-solar-500 to-eco-500 text-white">
        <Container>
          <div className="text-center max-w-3xl mx-auto">
            <h2 className="text-4xl lg:text-5xl font-bold mb-6">
              Ready to Start Mining?
            </h2>
            <p className="text-xl mb-8 opacity-90">
              Join thousands of investors earning daily returns with our 
              sustainable mining platform. Get started in minutes.
            </p>
            <Link href="/register">
              <Button 
                size="xl" 
                variant="secondary"
                className="bg-white text-dark-900 hover:bg-gray-100"
              >
                Create Account Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </Container>
      </section>

      {/* Footer */}
      <footer className="bg-dark-900 text-white py-12">
        <Container>
          <Grid cols={{ default: 1, md: 4 }} gap={8}>
            <div>
              <Flex align="center" gap={2} className="mb-4">
                <SolarPanel className="h-8 w-8 text-solar-400" />
                <span className="text-2xl font-bold">HashCoreX</span>
              </Flex>
              <p className="text-gray-400">
                Sustainable cryptocurrency mining powered by renewable energy.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Platform</h4>
              <div className="space-y-2">
                <Link href="/about" className="block text-gray-400 hover:text-white transition-colors">
                  About Us
                </Link>
                <Link href="/how-it-works" className="block text-gray-400 hover:text-white transition-colors">
                  How It Works
                </Link>
                <Link href="/pricing" className="block text-gray-400 hover:text-white transition-colors">
                  Pricing
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <div className="space-y-2">
                <Link href="/contact" className="block text-gray-400 hover:text-white transition-colors">
                  Contact Us
                </Link>
                <Link href="/faq" className="block text-gray-400 hover:text-white transition-colors">
                  FAQ
                </Link>
                <Link href="/help" className="block text-gray-400 hover:text-white transition-colors">
                  Help Center
                </Link>
              </div>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Legal</h4>
              <div className="space-y-2">
                <Link href="/privacy" className="block text-gray-400 hover:text-white transition-colors">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="block text-gray-400 hover:text-white transition-colors">
                  Terms of Service
                </Link>
                <Link href="/compliance" className="block text-gray-400 hover:text-white transition-colors">
                  Compliance
                </Link>
              </div>
            </div>
          </Grid>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 HashCoreX. All rights reserved.</p>
          </div>
        </Container>
      </footer>
    </div>
  );
};
