# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/hashcorex"
DIRECT_URL="postgresql://username:password@localhost:5432/hashcorex"

# Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY="your-firebase-api-key"
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN="your-project.firebaseapp.com"
NEXT_PUBLIC_FIREBASE_PROJECT_ID="your-project-id"
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET="your-project.appspot.com"
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID="your-sender-id"
NEXT_PUBLIC_FIREBASE_APP_ID="your-app-id"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="7d"

# Encryption Keys
ENCRYPTION_KEY="your-32-character-encryption-key-here"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"
ADMIN_PASSWORD="fjg53hHgfk"

# Platform Settings
NEXT_PUBLIC_APP_NAME="HashCoreX"
NEXT_PUBLIC_APP_URL="http://localhost:3000"

# Mining Configuration
DEFAULT_THS_PRICE="50"
DEFAULT_ROI_MIN="0.6"
DEFAULT_ROI_MAX="1.1"
MINIMUM_PURCHASE="50"
MINIMUM_WITHDRAWAL="10"

# Binary System Configuration
DIRECT_REFERRAL_BONUS="10"
BINARY_POOL_PERCENTAGE="30"
MAX_BINARY_POINTS_PER_SIDE="2000"

# USDT Configuration (for withdrawals)
USDT_NETWORK="TRC20"

# Email Configuration (for notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"

# Development
NODE_ENV="development"
