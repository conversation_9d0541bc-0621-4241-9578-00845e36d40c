{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/hooks/useAuth.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, createContext, useContext, ReactNode } from 'react';\nimport { User } from '@/types';\n\ninterface AuthContextType {\n  user: User | null;\n  loading: boolean;\n  login: (email: string, password: string) => Promise<void>;\n  register: (email: string, password: string, confirmPassword: string, referralCode?: string) => Promise<void>;\n  logout: () => Promise<void>;\n  refreshUser: () => Promise<void>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const AuthProvider = ({ children }: { children: ReactNode }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  // Check authentication status on mount\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  const checkAuth = async () => {\n    try {\n      const response = await fetch('/api/auth/me', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setUser(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const login = async (email: string, password: string) => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({ email, password }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Login failed');\n      }\n\n      setUser(data.data.user);\n    } catch (error) {\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const register = async (\n    email: string,\n    password: string,\n    confirmPassword: string,\n    referralCode?: string\n  ) => {\n    setLoading(true);\n    try {\n      const response = await fetch('/api/auth/register', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          email,\n          password,\n          confirmPassword,\n          referralCode,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Registration failed');\n      }\n\n      // Auto-login after successful registration\n      await login(email, password);\n    } catch (error) {\n      throw error;\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    try {\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      setUser(null);\n    }\n  };\n\n  const refreshUser = async () => {\n    await checkAuth();\n  };\n\n  const value = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    refreshUser,\n  };\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;\n};\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAcA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,eAAe,CAAC,EAAE,QAAQ,EAA2B;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,QAAQ,KAAK,IAAI;gBACnB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBAAE;oBAAO;gBAAS;YACzC;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,QAAQ,KAAK,IAAI,CAAC,IAAI;QACxB,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,WAAW,OACf,OACA,UACA,iBACA;QAEA,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;oBACA;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,2CAA2C;YAC3C,MAAM,MAAM,OAAO;QACrB,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,MAAM,oBAAoB;gBAC9B,QAAQ;gBACR,aAAa;YACf;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,MAAM,cAAc;QAClB,MAAM;IACR;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/node_modules/next/src/server/route-modules/app-page/module.compiled.js"], "sourcesContent": ["if (process.env.NEXT_RUNTIME === 'edge') {\n  module.exports = require('next/dist/server/route-modules/app-page/module.js')\n} else {\n  if (process.env.__NEXT_EXPERIMENTAL_REACT) {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo-experimental.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page-experimental.runtime.prod.js')\n      }\n    }\n  } else {\n    if (process.env.NODE_ENV === 'development') {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.dev.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.dev.js')\n      }\n    } else {\n      if (process.env.TURBOPACK) {\n        module.exports = require('next/dist/compiled/next-server/app-page-turbo.runtime.prod.js')\n      } else {\n        module.exports = require('next/dist/compiled/next-server/app-page.runtime.prod.js')\n      }\n    }\n  }\n}\n"], "names": ["process", "env", "NEXT_RUNTIME", "module", "exports", "require", "__NEXT_EXPERIMENTAL_REACT", "NODE_ENV", "TURBOPACK"], "mappings": ";AAAA,IAAIA,QAAQC,GAAG,CAACC,YAAY,KAAK,MAAQ;;AAEzC,OAAO;IACL,IAAIF,QAAQC,GAAG,CAACK,uBAA2B,EAAF;;IAczC,OAAO;QACL,IAAIN,QAAQC,GAAG,CAACM,QAAQ,KAAK,WAAe;YAC1C,IAAIP,QAAQC,GAAG,CAACO,SAAS,eAAE;gBACzBL,OAAOC,OAAO,GAAGC,QAAQ;YAC3B,OAAO;;YAEP;QACF,OAAO;;QAMP;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored['react-ssr'].React\n"], "names": ["module", "exports", "require", "vendored", "React"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CAAC,YAAY,CAACC,KAAK", "ignoreList": [0], "debugId": null}}]}