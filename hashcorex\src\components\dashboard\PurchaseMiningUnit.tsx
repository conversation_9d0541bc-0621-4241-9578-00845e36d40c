'use client';

import React, { useState, useEffect } from 'react';
import { Button, Input, Card, CardHeader, CardTitle, CardContent } from '@/components/ui';
import { MiningRig, Cryptocurrency } from '@/components/icons';
import { Calculator, DollarSign, Zap } from 'lucide-react';
import { formatCurrency, formatNumber } from '@/lib/utils';

interface PurchaseMiningUnitProps {
  onPurchaseComplete?: () => void;
}

export const PurchaseMiningUnit: React.FC<PurchaseMiningUnitProps> = ({
  onPurchaseComplete,
}) => {
  const [formData, setFormData] = useState({
    thsAmount: '',
    investmentAmount: '',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [thsPrice, setThsPrice] = useState(50); // Default price
  const [roiRange, setRoiRange] = useState({ min: 0.6, max: 1.1 });

  // Fetch current TH/s price and ROI range
  useEffect(() => {
    fetchPricing();
  }, []);

  const fetchPricing = async () => {
    try {
      const response = await fetch('/api/admin/settings/pricing');
      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setThsPrice(data.data.thsPrice);
          setRoiRange(data.data.roiRange);
        }
      }
    } catch (error) {
      console.error('Failed to fetch pricing:', error);
    }
  };

  const handleThsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const ths = parseFloat(e.target.value) || 0;
    const investment = ths * thsPrice;
    
    setFormData({
      thsAmount: e.target.value,
      investmentAmount: investment > 0 ? investment.toFixed(2) : '',
    });
  };

  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const investment = parseFloat(e.target.value) || 0;
    const ths = investment / thsPrice;
    
    setFormData({
      thsAmount: ths > 0 ? ths.toFixed(4) : '',
      investmentAmount: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const thsAmount = parseFloat(formData.thsAmount);
      const investmentAmount = parseFloat(formData.investmentAmount);

      if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {
        throw new Error('Please enter valid amounts');
      }

      if (investmentAmount < 50) {
        throw new Error('Minimum purchase amount is $50');
      }

      const response = await fetch('/api/mining-units', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          thsAmount,
          investmentAmount,
        }),
      });

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error || 'Purchase failed');
      }

      // Reset form
      setFormData({
        thsAmount: '',
        investmentAmount: '',
      });

      // Notify parent component
      if (onPurchaseComplete) {
        onPurchaseComplete();
      }

    } catch (err: any) {
      setError(err.message || 'Purchase failed');
    } finally {
      setLoading(false);
    }
  };

  const calculateEstimatedEarnings = () => {
    const investment = parseFloat(formData.investmentAmount) || 0;
    if (investment <= 0) return { daily: 0, weekly: 0, monthly: 0 };

    const avgROI = (roiRange.min + roiRange.max) / 2;
    const daily = (investment * avgROI) / 100;
    
    return {
      daily,
      weekly: daily * 7,
      monthly: daily * 30,
    };
  };

  const estimatedEarnings = calculateEstimatedEarnings();

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <MiningRig className="h-6 w-6 text-solar-500" />
          <span>Purchase Mining Power</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
              {error}
            </div>
          )}

          {/* Current Pricing Info */}
          <div className="bg-solar-50 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Current TH/s Price</span>
              <span className="text-lg font-bold text-solar-600">
                {formatCurrency(thsPrice)} / TH/s
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium text-gray-700">Daily ROI Range</span>
              <span className="text-sm font-semibold text-eco-600">
                {roiRange.min}% - {roiRange.max}%
              </span>
            </div>
          </div>

          {/* Input Fields */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Input
              label="TH/s Amount"
              type="number"
              step="0.0001"
              min="0"
              value={formData.thsAmount}
              onChange={handleThsChange}
              placeholder="Enter TH/s amount"
              leftIcon={<Zap className="h-4 w-4" />}
            />

            <Input
              label="Investment Amount (USD)"
              type="number"
              step="0.01"
              min="50"
              value={formData.investmentAmount}
              onChange={handleInvestmentChange}
              placeholder="Enter investment amount"
              leftIcon={<DollarSign className="h-4 w-4" />}
            />
          </div>

          {/* Estimated Earnings */}
          {estimatedEarnings.daily > 0 && (
            <div className="bg-eco-50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
                <Calculator className="h-4 w-4 mr-2" />
                Estimated Earnings (Average ROI: {formatNumber((roiRange.min + roiRange.max) / 2, 1)}%)
              </h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-lg font-bold text-eco-600">
                    {formatCurrency(estimatedEarnings.daily)}
                  </div>
                  <div className="text-xs text-gray-600">Daily</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-eco-600">
                    {formatCurrency(estimatedEarnings.weekly)}
                  </div>
                  <div className="text-xs text-gray-600">Weekly</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-eco-600">
                    {formatCurrency(estimatedEarnings.monthly)}
                  </div>
                  <div className="text-xs text-gray-600">Monthly</div>
                </div>
              </div>
            </div>
          )}

          {/* Important Notes */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="text-sm font-medium text-gray-700 mb-2">Important Notes:</h4>
            <ul className="text-xs text-gray-600 space-y-1">
              <li>• Minimum purchase: $50</li>
              <li>• Mining units are active for 12 months</li>
              <li>• Units expire when 5x investment is earned</li>
              <li>• Weekly payouts every Saturday at 15:00 UTC</li>
              <li>• ROI varies daily based on mining performance</li>
            </ul>
          </div>

          <Button
            type="submit"
            size="lg"
            className="w-full"
            loading={loading}
            disabled={!formData.thsAmount || !formData.investmentAmount || parseFloat(formData.investmentAmount) < 50}
          >
            <Cryptocurrency className="h-5 w-5 mr-2" />
            Purchase Mining Unit
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};
