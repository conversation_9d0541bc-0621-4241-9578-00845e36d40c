{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextMidnight = new Date();\n  \n  // Set to next midnight UTC\n  nextMidnight.setUTCDate(now.getUTCDate() + 1);\n  nextMidnight.setUTCHours(0, 0, 0, 0);\n  \n  const diff = nextMidnight.getTime() - now.getTime();\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAKd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,2BAA2B;IAC3B,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK;IAC3C,aAAa,WAAW,CAAC,GAAG,GAAG,GAAG;IAElC,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAO;QAAS;IAAQ;AACnC", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-gradient-to-r from-solar-500 to-solar-600 text-white hover:from-solar-600 hover:to-solar-700 focus:ring-solar-500 animate-pulse-glow',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-500',\n        success: 'bg-gradient-to-r from-eco-500 to-eco-600 text-white hover:from-eco-600 hover:to-eco-700 focus:ring-eco-500',\n        danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500',\n        outline: 'border-2 border-solar-500 bg-transparent text-solar-600 hover:bg-solar-500 hover:text-white focus:ring-solar-500 backdrop-blur-sm',\n        ghost: 'text-gray-600 hover:bg-solar-50 hover:text-solar-700 focus:ring-solar-500 rounded-lg',\n        link: 'text-solar-600 underline-offset-4 hover:underline focus:ring-solar-500 hover:text-solar-700',\n        premium: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 focus:ring-purple-500',\n        glass: 'glass-morphism text-dark-900 hover:bg-white/20 backdrop-blur-xl border border-white/20',\n      },\n      size: {\n        sm: 'h-10 px-4 text-sm rounded-lg',\n        md: 'h-12 px-6 text-base rounded-xl',\n        lg: 'h-14 px-8 text-lg rounded-xl',\n        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n        icon: 'h-12 w-12 rounded-xl',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <div className=\"mr-2\">\n            <div className=\"spinner\" />\n          </div>\n        )}\n        {leftIcon && !loading && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {rightIcon && !loading && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,yQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;YACR,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,YAAY,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,aAAa,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = 'CardHeader';\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode;\n}\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn('text-xl font-semibold leading-none tracking-tight text-dark-900', className)}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-gray-500', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = 'CardDescription';\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = 'CardContent';\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG;AAMzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300',\n              leftIcon && 'pl-12',\n              rightIcon && 'pr-12',\n              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;kCAGrC,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8UACA,YAAY,SACZ,aAAa,SACb,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;YAItC,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 523, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showCloseButton?: boolean;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n}) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div\n        className={cn(\n          'relative w-full bg-white rounded-xl shadow-xl transform transition-all',\n          sizeClasses[size]\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-dark-900\">{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onClose}\n              className=\"h-8 w-8 rounded-full\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\nexport { Modal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACvB;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;YACvC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,6BACJ,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WAAW,CAAC,KAAK;gBAEnB,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;4BACpD,iCACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;GArFM;KAAA", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className, text }) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-gray-300 border-t-solar-500',\n          sizeClasses[size]\n        )}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport interface LoadingOverlayProps {\n  isLoading: boolean;\n  text?: string;\n  children: React.ReactNode;\n}\n\nconst LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  isLoading,\n  text = 'Loading...',\n  children,\n}) => {\n  return (\n    <div className=\"relative\">\n      {children}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\">\n          <Loading text={text} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { Loading, LoadingOverlay };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAWA,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,WAAW,CAAC,KAAK;;;;;;YAGpB,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;KApBM;AA4BN,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,OAAO,YAAY,EACnB,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;;YACZ;YACA,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAQ,MAAM;;;;;;;;;;;;;;;;;AAKzB;MAfM", "debugId": null}}, {"offset": {"line": 753, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/index.ts"], "sourcesContent": ["export { Button, buttonVariants, type ButtonProps } from './Button';\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';\nexport { Input, type InputProps } from './Input';\nexport { Modal, type ModalProps } from './Modal';\nexport { Loading, LoadingOverlay, type LoadingProps } from './Loading';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 786, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Container.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div\n      className={cn(\n        'mx-auto px-4 sm:px-6 lg:px-8',\n        sizeClasses[size],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Container };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,WAAW,CAAC,KAAK,EACjB;kBAGD;;;;;;AAGP;KAxBM", "debugId": null}}, {"offset": {"line": 824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface GridProps {\n  children: React.ReactNode;\n  className?: string;\n  cols?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n  gap?: number;\n}\n\nconst Grid: React.FC<GridProps> = ({ \n  children, \n  className, \n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}) => {\n  const getGridClasses = () => {\n    const classes = ['grid'];\n    \n    // Default columns\n    if (cols.default) {\n      classes.push(`grid-cols-${cols.default}`);\n    }\n    \n    // Responsive columns\n    if (cols.sm) {\n      classes.push(`sm:grid-cols-${cols.sm}`);\n    }\n    if (cols.md) {\n      classes.push(`md:grid-cols-${cols.md}`);\n    }\n    if (cols.lg) {\n      classes.push(`lg:grid-cols-${cols.lg}`);\n    }\n    if (cols.xl) {\n      classes.push(`xl:grid-cols-${cols.xl}`);\n    }\n    \n    // Gap\n    classes.push(`gap-${gap}`);\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getGridClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface GridItemProps {\n  children: React.ReactNode;\n  className?: string;\n  span?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n}\n\nconst GridItem: React.FC<GridItemProps> = ({ \n  children, \n  className, \n  span \n}) => {\n  const getSpanClasses = () => {\n    if (!span) return '';\n    \n    const classes = [];\n    \n    if (span.default) {\n      classes.push(`col-span-${span.default}`);\n    }\n    if (span.sm) {\n      classes.push(`sm:col-span-${span.sm}`);\n    }\n    if (span.md) {\n      classes.push(`md:col-span-${span.md}`);\n    }\n    if (span.lg) {\n      classes.push(`lg:col-span-${span.lg}`);\n    }\n    if (span.xl) {\n      classes.push(`xl:col-span-${span.xl}`);\n    }\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getSpanClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Grid, GridItem };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAkBA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACR;IACC,MAAM,iBAAiB;QACrB,MAAM,UAAU;YAAC;SAAO;QAExB,kBAAkB;QAClB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC1C;QAEA,qBAAqB;QACrB,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QAEA,MAAM;QACN,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK;QAEzB,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;KAvCM;AAqDN,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,SAAS,EACT,IAAI,EACL;IACC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,UAAU,EAAE;QAElB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;QACzC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QAEA,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;MAlCM", "debugId": null}}, {"offset": {"line": 917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Flex.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface FlexProps {\n  children: React.ReactNode;\n  className?: string;\n  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';\n  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';\n  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';\n  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';\n  gap?: number;\n}\n\nconst Flex: React.FC<FlexProps> = ({\n  children,\n  className,\n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = 'nowrap',\n  gap = 0,\n}) => {\n  const directionClasses = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const alignClasses = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline',\n  };\n\n  const justifyClasses = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly',\n  };\n\n  const wrapClasses = {\n    wrap: 'flex-wrap',\n    nowrap: 'flex-nowrap',\n    'wrap-reverse': 'flex-wrap-reverse',\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex',\n        directionClasses[direction],\n        alignClasses[align],\n        justifyClasses[justify],\n        wrapClasses[wrap],\n        gap > 0 && `gap-${gap}`,\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Flex };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,OAAO,EACf,UAAU,OAAO,EACjB,OAAO,QAAQ,EACf,MAAM,CAAC,EACR;IACC,MAAM,mBAAmB;QACvB,KAAK;QACL,KAAK;QACL,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,QAAQ;QACR,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,CAAC,UAAU,EAC3B,YAAY,CAAC,MAAM,EACnB,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,EACvB;kBAGD;;;;;;AAGP;KAtDM", "debugId": null}}, {"offset": {"line": 974, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/index.ts"], "sourcesContent": ["export { Container } from './Container';\nexport { Grid, GridItem } from './Grid';\nexport { Flex } from './Flex';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/SolarPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"2\" y1=\"8\" x2=\"22\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"16\" x2=\"22\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"6\" y1=\"4\" x2=\"6\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"4\" x2=\"10\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"4\" x2=\"14\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"4\" x2=\"18\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <circle cx=\"20\" cy=\"2\" r=\"1\" fill=\"currentColor\"/>\n      <path d=\"M19 1l1 1-1 1-1-1z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,aAAkC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACtE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,GAAE;gBAAqB,MAAK;;;;;;;;;;;;AAGxC;KAtBa", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/MiningRig.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <rect x=\"4\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"4\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <circle cx=\"20\" cy=\"4\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"20\" y1=\"4\" x2=\"20\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"2\" x2=\"22\" y2=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"19\" y1=\"1\" x2=\"21\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n      <line x1=\"21\" y1=\"1\" x2=\"19\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,YAAiC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACrE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG5E;KA1Ba", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/Cryptocurrency.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h8M12 8v8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"12\" y1=\"6\" x2=\"12\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n\nexport const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"10\" y1=\"6\" x2=\"10\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"16\" x2=\"10\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"6\" x2=\"14\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"16\" x2=\"14\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAsC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAC1E,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAiB,QAAO;gBAAe,aAAY;;;;;;0BAC3D,6LAAC;gBAAK,GAAE;gBAAsC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACzF,6LAAC;gBAAK,GAAE;gBAAuC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1F,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;KAlBa;AAoBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAkE,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrH,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/EcoFriendly.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n\nexport const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M14 16l-3 3 3 3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8.293 13.596L7.196 9.5l3.1 1.598\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M16 8l3-3-3-3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n    </svg>\n  );\n};\n\nexport const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <line x1=\"12\" y1=\"12\" x2=\"12\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"10\" y1=\"22\" x2=\"14\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AASO,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAChE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAwD,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3G,6LAAC;gBAAK,GAAE;gBAA8C,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACjG,6LAAC;gBAAK,GAAE;gBAA4C,MAAK;;;;;;;;;;;;AAG/D;KAfa;AAiBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAA6E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAChI,6LAAC;gBAAK,GAAE;gBAA2E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC9H,6LAAC;gBAAK,GAAE;gBAAkB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrE,6LAAC;gBAAK,GAAE;gBAAoC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACvF,6LAAC;gBAAK,GAAE;gBAAuF,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1I,6LAAC;gBAAK,GAAE;gBAAgB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;;;;;;;AAGzE;MAlBa;AAoBN,MAAM,cAAmC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACvE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;;;;;;0BACnC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/index.ts"], "sourcesContent": ["export { SolarPanel } from './SolarPanel';\nexport { MiningRig } from './MiningRig';\nexport { Cryptocurrency, Bitcoin } from './Cryptocurrency';\nexport { Leaf, Recycle, WindTurbine } from './EcoFriendly';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/%28public%29/how-it-works/page.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { But<PERSON> } from '@/components/ui';\nimport { Container, Grid, Flex } from '@/components/layout';\nimport { SolarPanel, MiningRig, Cryptocurrency } from '@/components/icons';\nimport { ArrowLeft, ArrowRight, UserPlus, Shield, DollarSign, TrendingUp } from 'lucide-react';\n\nexport default function HowItWorksPage() {\n  return (\n    <div className=\"min-h-screen bg-white\">\n      {/* Navigation */}\n      <nav className=\"bg-white shadow-sm border-b border-gray-100\">\n        <Container>\n          <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <SolarPanel className=\"h-8 w-8 text-solar-500\" />\n              <span className=\"text-2xl font-bold text-dark-900\">HashCoreX</span>\n            </Link>\n            <Flex align=\"center\" gap={6}>\n              <Link href=\"/\" className=\"text-gray-600 hover:text-dark-900 transition-colors\">\n                <ArrowLeft className=\"h-4 w-4 mr-2 inline\" />\n                Back to Home\n              </Link>\n              <Link href=\"/login\">\n                <Button variant=\"outline\" size=\"sm\">\n                  Login\n                </Button>\n              </Link>\n              <Link href=\"/register\">\n                <Button size=\"sm\">\n                  Get Started\n                </Button>\n              </Link>\n            </Flex>\n          </Flex>\n        </Container>\n      </nav>\n\n      {/* Hero Section */}\n      <section className=\"py-20 bg-gradient-to-br from-solar-50 to-eco-50\">\n        <Container>\n          <div className=\"text-center max-w-4xl mx-auto\">\n            <h1 className=\"text-5xl lg:text-6xl font-bold text-dark-900 mb-6\">\n              How <span className=\"text-solar-500\">HashCoreX</span> Works\n            </h1>\n            <p className=\"text-xl text-gray-600 leading-relaxed\">\n              Simple, transparent, and profitable. Learn how our solar-powered \n              mining platform generates real returns for investors.\n            </p>\n          </div>\n        </Container>\n      </section>\n\n      {/* Process Steps */}\n      <section className=\"py-20\">\n        <Container>\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-dark-900 mb-4\">\n              Get Started in 4 Simple Steps\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-2xl mx-auto\">\n              From registration to earning, here's how you can start mining \n              with renewable energy.\n            </p>\n          </div>\n\n          <div className=\"max-w-5xl mx-auto\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n              {/* Step 1 */}\n              <div className=\"text-center relative\">\n                <div className=\"inline-flex items-center justify-center w-20 h-20 bg-solar-100 rounded-full mb-6\">\n                  <UserPlus className=\"h-10 w-10 text-solar-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-dark-900 mb-3\">1. Register</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Create your account with email verification and secure your profile.\n                </p>\n                <div className=\"hidden lg:block absolute top-10 -right-4 text-solar-300\">\n                  <ArrowRight className=\"h-6 w-6\" />\n                </div>\n              </div>\n\n              {/* Step 2 */}\n              <div className=\"text-center relative\">\n                <div className=\"inline-flex items-center justify-center w-20 h-20 bg-eco-100 rounded-full mb-6\">\n                  <Shield className=\"h-10 w-10 text-eco-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-dark-900 mb-3\">2. Verify</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Complete KYC verification by uploading your ID and selfie for security.\n                </p>\n                <div className=\"hidden lg:block absolute top-10 -right-4 text-eco-300\">\n                  <ArrowRight className=\"h-6 w-6\" />\n                </div>\n              </div>\n\n              {/* Step 3 */}\n              <div className=\"text-center relative\">\n                <div className=\"inline-flex items-center justify-center w-20 h-20 bg-solar-100 rounded-full mb-6\">\n                  <DollarSign className=\"h-10 w-10 text-solar-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-dark-900 mb-3\">3. Buy TH/s</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Purchase mining power starting from $50 using USDT (TRC20).\n                </p>\n                <div className=\"hidden lg:block absolute top-10 -right-4 text-solar-300\">\n                  <ArrowRight className=\"h-6 w-6\" />\n                </div>\n              </div>\n\n              {/* Step 4 */}\n              <div className=\"text-center\">\n                <div className=\"inline-flex items-center justify-center w-20 h-20 bg-eco-100 rounded-full mb-6\">\n                  <TrendingUp className=\"h-10 w-10 text-eco-600\" />\n                </div>\n                <h3 className=\"text-xl font-semibold text-dark-900 mb-3\">4. Earn</h3>\n                <p className=\"text-gray-600 mb-4\">\n                  Receive daily mining returns and weekly payouts to your wallet.\n                </p>\n              </div>\n            </div>\n          </div>\n        </Container>\n      </section>\n\n      {/* ROI Explanation */}\n      <section className=\"py-20 bg-gray-50\">\n        <Container>\n          <Grid cols={{ default: 1, lg: 2 }} gap={12} className=\"items-center\">\n            <div>\n              <h2 className=\"text-4xl font-bold text-dark-900 mb-6\">\n                Understanding Your Returns\n              </h2>\n              <div className=\"space-y-6\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"flex-shrink-0 w-8 h-8 bg-solar-500 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    1\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-dark-900 mb-2\">Daily Mining ROI</h3>\n                    <p className=\"text-gray-600\">\n                      Earn 0.6% - 1.1% daily returns based on actual mining performance \n                      from our solar-powered facilities.\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"flex-shrink-0 w-8 h-8 bg-eco-500 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    2\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-dark-900 mb-2\">Weekly Payouts</h3>\n                    <p className=\"text-gray-600\">\n                      All accumulated earnings are transferred to your wallet every \n                      Saturday at 15:00 UTC automatically.\n                    </p>\n                  </div>\n                </div>\n\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"flex-shrink-0 w-8 h-8 bg-solar-500 rounded-full flex items-center justify-center text-white text-sm font-bold\">\n                    3\n                  </div>\n                  <div>\n                    <h3 className=\"text-lg font-semibold text-dark-900 mb-2\">12-Month Duration</h3>\n                    <p className=\"text-gray-600\">\n                      Mining units are active for 12 months or until you earn 5x \n                      your initial investment, whichever comes first.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white rounded-2xl p-8 shadow-lg\">\n              <h3 className=\"text-2xl font-bold text-dark-900 mb-6 text-center\">\n                Example Investment\n              </h3>\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center py-3 border-b border-gray-100\">\n                  <span className=\"text-gray-600\">Investment Amount</span>\n                  <span className=\"font-semibold text-dark-900\">$1,000</span>\n                </div>\n                <div className=\"flex justify-between items-center py-3 border-b border-gray-100\">\n                  <span className=\"text-gray-600\">TH/s Purchased</span>\n                  <span className=\"font-semibold text-dark-900\">20 TH/s</span>\n                </div>\n                <div className=\"flex justify-between items-center py-3 border-b border-gray-100\">\n                  <span className=\"text-gray-600\">Daily ROI (0.8%)</span>\n                  <span className=\"font-semibold text-eco-600\">$8.00</span>\n                </div>\n                <div className=\"flex justify-between items-center py-3 border-b border-gray-100\">\n                  <span className=\"text-gray-600\">Weekly Earnings</span>\n                  <span className=\"font-semibold text-eco-600\">$56.00</span>\n                </div>\n                <div className=\"flex justify-between items-center py-3 bg-solar-50 rounded-lg px-4\">\n                  <span className=\"text-gray-600\">Monthly Potential</span>\n                  <span className=\"font-bold text-solar-600 text-lg\">$240.00</span>\n                </div>\n              </div>\n            </div>\n          </Grid>\n        </Container>\n      </section>\n\n      {/* Binary Network */}\n      <section className=\"py-20\">\n        <Container>\n          <div className=\"text-center mb-16\">\n            <h2 className=\"text-4xl font-bold text-dark-900 mb-4\">\n              Binary Referral Network\n            </h2>\n            <p className=\"text-xl text-gray-600 max-w-3xl mx-auto\">\n              Earn additional income by building your referral network with our \n              innovative binary system.\n            </p>\n          </div>\n\n          <Grid cols={{ default: 1, lg: 3 }} gap={8}>\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4\">\n                <span className=\"text-2xl font-bold text-solar-600\">10%</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-dark-900 mb-2\">Direct Referral</h3>\n              <p className=\"text-gray-600\">\n                Earn 10% commission on every direct referral's investment immediately.\n              </p>\n            </div>\n\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-eco-100 rounded-full mb-4\">\n                <span className=\"text-2xl font-bold text-eco-600\">30%</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-dark-900 mb-2\">Binary Pool</h3>\n              <p className=\"text-gray-600\">\n                30% of all platform investments go into the binary matching pool.\n              </p>\n            </div>\n\n            <div className=\"text-center p-6 bg-white rounded-xl shadow-sm border border-gray-100\">\n              <div className=\"inline-flex items-center justify-center w-16 h-16 bg-solar-100 rounded-full mb-4\">\n                <span className=\"text-lg font-bold text-solar-600\">Daily</span>\n              </div>\n              <h3 className=\"text-xl font-semibold text-dark-900 mb-2\">Binary Matching</h3>\n              <p className=\"text-gray-600\">\n                Daily matching at 12:00 AM UTC with up to 2,000 points per side.\n              </p>\n            </div>\n          </Grid>\n        </Container>\n      </section>\n\n      {/* CTA Section */}\n      <section className=\"py-20 bg-gradient-to-r from-solar-500 to-eco-500 text-white\">\n        <Container>\n          <div className=\"text-center max-w-3xl mx-auto\">\n            <h2 className=\"text-4xl lg:text-5xl font-bold mb-6\">\n              Ready to Start Mining?\n            </h2>\n            <p className=\"text-xl mb-8 opacity-90\">\n              Join thousands of investors earning daily returns with sustainable mining. \n              Get started in just a few minutes.\n            </p>\n            <Flex justify=\"center\" gap={4} className=\"flex-col sm:flex-row\">\n              <Link href=\"/register\">\n                <Button \n                  size=\"xl\" \n                  variant=\"secondary\"\n                  className=\"bg-white text-dark-900 hover:bg-gray-100 w-full sm:w-auto\"\n                >\n                  Create Account\n                </Button>\n              </Link>\n              <Link href=\"/about\">\n                <Button \n                  size=\"xl\" \n                  variant=\"outline\"\n                  className=\"border-white text-white hover:bg-white hover:text-dark-900 w-full sm:w-auto\"\n                >\n                  Learn More\n                </Button>\n              </Link>\n            </Flex>\n          </div>\n        </Container>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAPA;;;;;;;AASe,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,4IAAA,CAAA,YAAS;8BACR,cAAA,6LAAC,uIAAA,CAAA,OAAI;wBAAC,SAAQ;wBAAU,OAAM;wBAAS,WAAU;;0CAC/C,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC,4IAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;kDACtB,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;;;;;;;0CAErD,6LAAC,uIAAA,CAAA,OAAI;gCAAC,OAAM;gCAAS,KAAK;;kDACxB,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAI,WAAU;;0DACvB,6LAAC,mNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAwB;;;;;;;kDAG/C,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAAK;;;;;;;;;;;kDAItC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CAAC,MAAK;sDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5B,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,4IAAA,CAAA,YAAS;8BACR,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAoD;kDAC5D,6LAAC;wCAAK,WAAU;kDAAiB;;;;;;oCAAgB;;;;;;;0CAEvD,6LAAC;gCAAE,WAAU;0CAAwC;;;;;;;;;;;;;;;;;;;;;;0BAS3D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,4IAAA,CAAA,YAAS;;sCACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,iNAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;0DAEtB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;kDAK1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;0DAExB,6LAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,6LAAC;gDAAE,WAAU;0DAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU5C,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,4IAAA,CAAA,YAAS;8BACR,cAAA,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;wBAAI,WAAU;;0CACpD,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgH;;;;;;kEAG/H,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAOjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAA8G;;;;;;kEAG7H,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;0DAOjC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgH;;;;;;kEAG/H,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAA2C;;;;;;0EACzD,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CASrC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAoD;;;;;;kDAGlE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA8B;;;;;;;;;;;;0DAEhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA8B;;;;;;;;;;;;0DAEhD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/D,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,4IAAA,CAAA,YAAS;;sCACR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,6LAAC;oCAAE,WAAU;8CAA0C;;;;;;;;;;;;sCAMzD,6LAAC,uIAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,SAAS;gCAAG,IAAI;4BAAE;4BAAG,KAAK;;8CACtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;sDAEtD,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAkC;;;;;;;;;;;sDAEpD,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;8CAK/B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAmC;;;;;;;;;;;sDAErD,6LAAC;4CAAG,WAAU;sDAA2C;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASrC,6LAAC;gBAAQ,WAAU;0BACjB,cAAA,6LAAC,4IAAA,CAAA,YAAS;8BACR,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CAGpD,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;0CAIvC,6LAAC,uIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAS,KAAK;gCAAG,WAAU;;kDACvC,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;kDAIH,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;KA1RwB", "debugId": null}}]}