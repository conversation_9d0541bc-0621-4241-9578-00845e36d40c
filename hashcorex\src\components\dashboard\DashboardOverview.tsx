'use client';

import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardContent, Button } from '@/components/ui';
import { Grid, GridItem } from '@/components/layout';
import { MiningRig, Cryptocurrency, SolarPanel } from '@/components/icons';
import { TrendingUp, Wallet, Users, Zap, Clock, Award } from 'lucide-react';
import { formatCurrency, formatTHS, getTimeUntilNextPayout } from '@/lib/utils';

interface DashboardStats {
  totalTHS: number;
  estimatedEarnings: {
    next7Days: number;
    next30Days: number;
    next365Days: number;
  };
  walletBalance: number;
  pendingEarnings: number;
  activeUnits: number;
  totalEarnings: number;
  directReferrals: number;
  binaryPoints: {
    leftPoints: number;
    rightPoints: number;
  };
}

export const DashboardOverview: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());

  useEffect(() => {
    fetchDashboardStats();
    
    // Update countdown every second
    const interval = setInterval(() => {
      setTimeUntilPayout(getTimeUntilNextPayout());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const fetchDashboardStats = async () => {
    try {
      // Fetch data from multiple endpoints
      const [walletRes, earningsRes, miningRes, referralRes] = await Promise.all([
        fetch('/api/wallet/balance', { credentials: 'include' }),
        fetch('/api/earnings', { credentials: 'include' }),
        fetch('/api/mining-units', { credentials: 'include' }),
        fetch('/api/referrals/tree?depth=1', { credentials: 'include' }),
      ]);

      const [walletData, earningsData, miningData, referralData] = await Promise.all([
        walletRes.json(),
        earningsRes.json(),
        miningRes.json(),
        referralRes.json(),
      ]);

      if (walletData.success && earningsData.success && miningData.success && referralData.success) {
        const totalTHS = miningData.data.reduce((sum: number, unit: any) => sum + unit.thsAmount, 0);
        
        setStats({
          totalTHS,
          estimatedEarnings: earningsData.data.estimatedEarnings,
          walletBalance: walletData.data.balance,
          pendingEarnings: walletData.data.pendingEarnings,
          activeUnits: miningData.data.length,
          totalEarnings: earningsData.data.totalEarnings,
          directReferrals: referralData.data.statistics.totalDirectReferrals,
          binaryPoints: referralData.data.statistics.binaryPoints,
        });
      }
    } catch (error) {
      console.error('Failed to fetch dashboard stats:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse">
            <div className="h-32 bg-gray-200 rounded-xl"></div>
          </div>
        ))}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <p className="text-gray-500">Failed to load dashboard data</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-gradient-to-r from-solar-500 to-eco-500 rounded-xl p-6 text-white">
        <h1 className="text-2xl font-bold mb-2">Welcome to HashCoreX</h1>
        <p className="text-solar-100">
          Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network.
        </p>
      </div>

      {/* Key Metrics */}
      <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Mining Power</p>
                <p className="text-2xl font-bold text-dark-900">
                  {formatTHS(stats.totalTHS)}
                </p>
              </div>
              <div className="h-12 w-12 bg-solar-100 rounded-full flex items-center justify-center">
                <Zap className="h-6 w-6 text-solar-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Wallet Balance</p>
                <p className="text-2xl font-bold text-eco-600">
                  {formatCurrency(stats.walletBalance)}
                </p>
              </div>
              <div className="h-12 w-12 bg-eco-100 rounded-full flex items-center justify-center">
                <Wallet className="h-6 w-6 text-eco-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Earnings</p>
                <p className="text-2xl font-bold text-dark-900">
                  {formatCurrency(stats.totalEarnings)}
                </p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Direct Referrals</p>
                <p className="text-2xl font-bold text-dark-900">
                  {stats.directReferrals}
                </p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </Grid>

      <Grid cols={{ default: 1, lg: 2 }} gap={6}>
        {/* Estimated Earnings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-eco-500" />
              <span>Estimated Earnings</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 7 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(stats.estimatedEarnings.next7Days)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 30 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(stats.estimatedEarnings.next30Days)}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-gray-600">Next 365 Days</span>
                <span className="font-semibold text-eco-600">
                  {formatCurrency(stats.estimatedEarnings.next365Days)}
                </span>
              </div>
            </div>
            <div className="mt-4 p-3 bg-eco-50 rounded-lg">
              <p className="text-xs text-eco-700">
                * Based on current mining units and average ROI
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Next Payout */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-solar-500" />
              <span>Next Payout</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-center">
              <p className="text-sm text-gray-600 mb-4">
                Weekly payout every Saturday at 15:00 UTC
              </p>
              <div className="grid grid-cols-4 gap-2">
                <div className="text-center">
                  <div className="text-xl font-bold text-solar-600">
                    {timeUntilPayout.days}
                  </div>
                  <div className="text-xs text-gray-500">Days</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-solar-600">
                    {timeUntilPayout.hours}
                  </div>
                  <div className="text-xs text-gray-500">Hours</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-solar-600">
                    {timeUntilPayout.minutes}
                  </div>
                  <div className="text-xs text-gray-500">Min</div>
                </div>
                <div className="text-center">
                  <div className="text-xl font-bold text-solar-600">
                    {timeUntilPayout.seconds}
                  </div>
                  <div className="text-xs text-gray-500">Sec</div>
                </div>
              </div>
              {stats.pendingEarnings > 0 && (
                <div className="mt-4 p-3 bg-solar-50 rounded-lg">
                  <p className="text-sm text-solar-700">
                    <strong>{formatCurrency(stats.pendingEarnings)}</strong> pending
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </Grid>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={{ default: 1, md: 3 }} gap={4}>
            <Button className="h-16 flex flex-col items-center justify-center space-y-2">
              <MiningRig className="h-6 w-6" />
              <span>Buy Mining Power</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-2">
              <Cryptocurrency className="h-6 w-6" />
              <span>Withdraw USDT</span>
            </Button>
            <Button variant="outline" className="h-16 flex flex-col items-center justify-center space-y-2">
              <Users className="h-6 w-6" />
              <span>Share Referral</span>
            </Button>
          </Grid>
        </CardContent>
      </Card>

      {/* Binary Points Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Award className="h-5 w-5 text-solar-500" />
            <span>Binary Network Summary</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Grid cols={{ default: 1, md: 3 }} gap={6}>
            <div className="text-center">
              <div className="text-2xl font-bold text-solar-600">
                {stats.binaryPoints.leftPoints}
              </div>
              <div className="text-sm text-gray-600">Left Points</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-solar-600">
                {stats.binaryPoints.rightPoints}
              </div>
              <div className="text-sm text-gray-600">Right Points</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-eco-600">
                {Math.min(stats.binaryPoints.leftPoints, stats.binaryPoints.rightPoints)}
              </div>
              <div className="text-sm text-gray-600">Potential Match</div>
            </div>
          </Grid>
        </CardContent>
      </Card>
    </div>
  );
};
