'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { AdminLayout } from '@/components/admin/AdminLayout';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { UserManagement } from '@/components/admin/UserManagement';
import { KYCReview } from '@/components/admin/KYCReview';
import { WithdrawalManagement } from '@/components/admin/WithdrawalManagement';
import { SystemSettings } from '@/components/admin/SystemSettings';
import { SystemLogs } from '@/components/admin/SystemLogs';
import { Loading } from '@/components/ui';
import { isAdmin } from '@/lib/auth';

export default function AdminPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isAdminUser, setIsAdminUser] = useState(false);
  const [checkingAdmin, setCheckingAdmin] = useState(true);

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
      return;
    }

    if (user) {
      checkAdminStatus();
    }
  }, [user, loading, router]);

  const checkAdminStatus = async () => {
    try {
      const response = await fetch('/api/admin/check', {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setIsAdminUser(data.isAdmin);
        
        if (!data.isAdmin) {
          router.push('/dashboard');
        }
      } else {
        router.push('/dashboard');
      }
    } catch (error) {
      console.error('Admin check failed:', error);
      router.push('/dashboard');
    } finally {
      setCheckingAdmin(false);
    }
  };

  if (loading || checkingAdmin) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading admin panel..." />
      </div>
    );
  }

  if (!user || !isAdminUser) {
    return null;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'dashboard':
        return <AdminDashboard />;
      case 'users':
        return <UserManagement />;
      case 'kyc':
        return <KYCReview />;
      case 'withdrawals':
        return <WithdrawalManagement />;
      case 'settings':
        return <SystemSettings />;
      case 'logs':
        return <SystemLogs />;
      default:
        return <AdminDashboard />;
    }
  };

  return (
    <AdminLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </AdminLayout>
  );
}
