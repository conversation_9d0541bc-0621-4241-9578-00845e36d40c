{"dependencies": {"@headlessui/react": "^2.2.4", "@heroicons/react": "^2.2.0", "@supabase/supabase-js": "^2.50.0", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "crypto-js": "^4.2.0", "date-fns": "^4.1.0", "firebase": "^11.9.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.518.0", "pg": "^8.16.1", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0"}, "devDependencies": {"@prisma/client": "^6.10.1", "@types/bcryptjs": "^2.4.6", "@types/crypto-js": "^4.2.2", "@types/jsonwebtoken": "^9.0.10", "prisma": "^6.10.1", "tsx": "^4.20.3"}}